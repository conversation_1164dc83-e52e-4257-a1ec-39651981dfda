import { useQuery } from '@tanstack/react-query';
import type { QueryFunction } from '@tanstack/react-query';
import { getVideoById } from '../api/videoApi';
import type { VideoDetails } from '../api/videoApi';

interface UseGetVideoDetailsResult {
  data?: VideoDetails;
  error: Error | null;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  isFetching: boolean;
  isFetched: boolean;
}

interface UseVideoDetailsOptions {
  enabled?: boolean;
}

type QueryKey = readonly ['video', string];

const fetchVideoDetails: QueryFunction<VideoDetails, QueryKey> = async ({ queryKey }) => {
  const [, videoId] = queryKey;
  return getVideoById(videoId);
};

export const useGetVideoDetails = (
  videoId: string,
  options: UseVideoDetailsOptions = {}
): UseGetVideoDetailsResult => {
  const { enabled = true } = options;

  const result = useQuery<VideoDetails, Error, VideoDetails, QueryKey>({
    queryKey: ['video', videoId] as const,
    queryFn: fetchVideoDetails,
    enabled: enabled && !!videoId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime in v5)
    refetchOnWindowFocus: false,
  });

  return {
    data: result.data,
    error: result.error,
    isLoading: result.isLoading,
    isError: result.isError,
    isSuccess: result.isSuccess,
    isFetching: result.isFetching,
    isFetched: result.isFetched,
  };
};
