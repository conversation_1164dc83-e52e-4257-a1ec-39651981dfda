import { useParams, useNavigate } from 'react-router-dom';
import { FiArrowLeft, FiClock, FiCalendar, FiFileText } from 'react-icons/fi';
import { useGetVideoDetails } from '../hooks/useGetVideoDetails';
import type { WordTiming } from '../api/videoApi';
import { formatTimeHMS } from '../utils/timeUtils';

const VideoDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { data: video, isLoading, isError, error } = useGetVideoDetails(id || '');

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}h ${mins}m ${secs}s`;
    }
    return `${mins}m ${secs}s`;
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (isError || !video) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Video Not Found
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {error?.message || 'The video you are looking for could not be found.'}
          </p>
          <button
            onClick={() => navigate('/')}
            className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            <FiArrowLeft className="w-4 h-4 mr-2" />
            Back to Videos
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <button
          onClick={() => navigate('/')}
          className="inline-flex items-center text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white mb-4"
        >
          <FiArrowLeft className="w-4 h-4 mr-2" />
          Back to Videos
        </button>
        
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          {video.title}
        </h1>
        
        {video.description && (
          <p className="text-gray-600 dark:text-gray-400 text-lg">
            {video.description}
          </p>
        )}
      </div>

      {/* Video Info */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <FiClock className="w-5 h-5 text-gray-500 mr-2" />
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Duration</p>
              <p className="font-medium text-gray-900 dark:text-white">
                {formatDuration(video.duration)}
              </p>
            </div>
          </div>
          
          <div className="flex items-center">
            <FiCalendar className="w-5 h-5 text-gray-500 mr-2" />
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Created</p>
              <p className="font-medium text-gray-900 dark:text-white">
                {new Date(video.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>
          
          <div className="flex items-center">
            <FiFileText className="w-5 h-5 text-gray-500 mr-2" />
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Segments</p>
              <p className="font-medium text-gray-900 dark:text-white">
                {video.transcriptSegments?.length || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Transcript */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Transcript with Word Timings
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Each word shows its timing information
          </p>
        </div>
        
        <div className="p-6">

          {video.transcriptSegments && video.transcriptSegments.length > 0 ? (
            <div className="space-y-6">
              {video.transcriptSegments.map((segment, segmentIndex) => (
                <div key={segment.id || segmentIndex} className="border-l-4 border-blue-500 pl-4">
                  <div className="flex items-center mb-3">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Segment {segmentIndex + 1}
                    </span>
                    <span className="ml-3 text-sm text-gray-500 dark:text-gray-400">
                      {formatTimeHMS(segment.startTime)} → {formatTimeHMS(segment.endTime)}
                    </span>
                  </div>

                  <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 pt-6 bg-gray-50 dark:bg-gray-800 min-h-[100px]">
                    {segment.words && segment.words.length > 0 ? (
                      segment.words.map((word: WordTiming, wordIndex: number) => (
                        <div
                          key={wordIndex}
                          className="relative inline-block mr-2.5 mb-6 cursor-pointer group"
                        >
                          {/* Time Display */}
                          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 whitespace-nowrap px-1 py-0.5 rounded transition-all duration-200 text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 bg-opacity-90 border border-transparent group-hover:bg-blue-50 dark:group-hover:bg-blue-900/30 group-hover:border-blue-200 dark:group-hover:border-blue-700 text-xs"
                            style={{ fontSize: '8px' }}>
                            {formatTimeHMS(word.startTime)}
                          </div>

                          {/* Word */}
                          <span className="transition-colors duration-200 text-gray-800 dark:text-gray-200 group-hover:text-gray-900 dark:group-hover:text-gray-100"
                            style={{ fontSize: '16px' }}>
                            {word.word}
                          </span>
                        </div>
                      ))
                    ) : (
                      <p className="text-gray-700 dark:text-gray-300">{segment.text}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : video.transcript ? (
            <div className="border-l-4 border-gray-300 pl-4">
              <div className="mb-3">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Full Transcript
                </span>
              </div>
              <div className="prose dark:prose-invert max-w-none">
                <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                  {video.transcript}
                </p>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p className="mb-2">No transcript available</p>
              <p className="text-sm">This video doesn't have transcript data</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VideoDetails;
