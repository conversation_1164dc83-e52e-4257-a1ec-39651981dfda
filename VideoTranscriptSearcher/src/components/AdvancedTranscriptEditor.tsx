import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>lock, FiPlus, FiMinus } from 'react-icons/fi';
import { 
  WordTiming, 
  formatTimeHMS, 
  parseTimeHMS, 
  calculateWordTimings, 
  updateWordTimingsWithStickPoint,
  getValidTimeForWord 
} from '../utils/timeUtils';

interface AdvancedTranscriptEditorProps {
  initialText?: string;
  duration: number;
  onChange: (wordTimings: WordTiming[]) => void;
  className?: string;
}

interface TimeInputModalProps {
  isOpen: boolean;
  currentTime: number;
  onSave: (time: number) => void;
  onClose: () => void;
  wordIndex: number;
  word: string;
}

const TimeInputModal: React.FC<TimeInputModalProps> = ({
  isOpen,
  currentTime,
  onSave,
  onClose,
  wordIndex,
  word
}) => {
  const [timeInput, setTimeInput] = useState(formatTimeHMS(currentTime));
  
  useEffect(() => {
    setTimeInput(formatTimeHMS(currentTime));
  }, [currentTime, isOpen]);
  
  const handleSave = () => {
    const parsedTime = parseTimeHMS(timeInput);
    onSave(parsedTime);
    onClose();
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Set Time for "{word}"
        </h3>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Time (h:m:s or m:s)
          </label>
          <input
            type="text"
            value={timeInput}
            onChange={(e) => setTimeInput(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="0:30 or 1:30:45"
            autoFocus
          />
        </div>
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

const AdvancedTranscriptEditor: React.FC<AdvancedTranscriptEditorProps> = ({
  initialText = '',
  duration,
  onChange,
  className = ''
}) => {
  const [text, setText] = useState(initialText);
  const [wordTimings, setWordTimings] = useState<WordTiming[]>([]);
  const [selectedWordIndex, setSelectedWordIndex] = useState<number | null>(null);
  const [isTimeModalOpen, setIsTimeModalOpen] = useState(false);
  const [draggedWordIndex, setDraggedWordIndex] = useState<number | null>(null);
  const [fontSize, setFontSize] = useState(18);
  
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Calculate initial word timings when text or duration changes
  useEffect(() => {
    if (text.trim()) {
      const newWordTimings = calculateWordTimings(text, duration);
      setWordTimings(newWordTimings);
      onChange(newWordTimings);
    } else {
      setWordTimings([]);
      onChange([]);
    }
  }, [text, duration]);
  
  const handleTextChange = (newText: string) => {
    setText(newText);
  };
  
  const handleStickClick = (wordIndex: number) => {
    setSelectedWordIndex(wordIndex);
    setIsTimeModalOpen(true);
  };
  
  const handleTimeUpdate = (wordIndex: number, newTime: number) => {
    const validTime = getValidTimeForWord(wordIndex, newTime, wordTimings, duration);
    const updatedTimings = updateWordTimingsWithStickPoint(wordTimings, wordIndex, validTime, duration);
    setWordTimings(updatedTimings);
    onChange(updatedTimings);
  };
  
  const handleMouseDown = (wordIndex: number, event: React.MouseEvent) => {
    event.preventDefault();
    setDraggedWordIndex(wordIndex);
  };
  
  const handleMouseMove = (event: React.MouseEvent) => {
    if (draggedWordIndex === null || !containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const relativeX = event.clientX - rect.left;
    const containerWidth = rect.width;
    const timeRatio = relativeX / containerWidth;
    const newTime = Math.max(0, Math.min(duration, timeRatio * duration));
    
    handleTimeUpdate(draggedWordIndex, newTime);
  };
  
  const handleMouseUp = () => {
    setDraggedWordIndex(null);
  };
  
  useEffect(() => {
    if (draggedWordIndex !== null) {
      document.addEventListener('mousemove', handleMouseMove as any);
      document.addEventListener('mouseup', handleMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove as any);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [draggedWordIndex]);
  
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Controls */}
      <div className="flex items-center justify-between bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <FiClock className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600 dark:text-gray-400">
              Duration: {formatTimeHMS(duration)}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">Font Size:</span>
            <button
              onClick={() => setFontSize(Math.max(14, fontSize - 2))}
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <FiMinus className="w-3 h-3" />
            </button>
            <span className="text-sm text-gray-600 dark:text-gray-400 min-w-[2rem] text-center">
              {fontSize}px
            </span>
            <button
              onClick={() => setFontSize(Math.min(26, fontSize + 2))}
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <FiPlus className="w-3 h-3" />
            </button>
          </div>
        </div>
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {wordTimings.length} words • {wordTimings.filter(wt => wt.isStickPoint).length} stick points
        </div>
      </div>
      
      {/* Text Input */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Transcript Text
        </label>
        <textarea
          value={text}
          onChange={(e) => handleTextChange(e.target.value)}
          rows={4}
          className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          placeholder="Paste your transcript here. The app will automatically calculate word timings based on the duration."
        />
      </div>
      
      {/* Word Timeline Editor */}
      {wordTimings.length > 0 && (
        <div
          ref={containerRef}
          className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 pt-6 bg-gray-50 dark:bg-gray-800 min-h-[200px]"
        >
          {wordTimings.map((wordTiming, index) => (
            <div
              key={index}
              className="relative inline-block mr-2.5 mb-6 cursor-pointer group"
              onClick={() => handleStickClick(index)}
            >
              {/* Time Display */}
              <div className={`absolute -top-3 left-1/2 transform -translate-x-1/2 whitespace-nowrap px-1 py-0.5 rounded transition-all duration-200 ${
                wordTiming.isStickPoint
                  ? 'text-blue-600 dark:text-blue-400 font-semibold bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 text-xs'
                  : 'text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-700 bg-opacity-90 border border-transparent group-hover:bg-blue-50 dark:group-hover:bg-blue-900/30 group-hover:border-blue-200 dark:group-hover:border-blue-700 text-xs'
              }`}
              style={{ fontSize: '8px' }}>
                {formatTimeHMS(wordTiming.startTime)}
              </div>

              {/* Word */}
              <span
                className={`transition-colors duration-200 ${
                  wordTiming.isStickPoint
                    ? 'font-semibold text-gray-900 dark:text-gray-100'
                    : 'text-gray-800 dark:text-gray-200 group-hover:text-gray-900 dark:group-hover:text-gray-100'
                }`}
                style={{ fontSize: `${fontSize}px` }}
              >
                {wordTiming.word}
              </span>
            </div>
          ))}
        </div>
      )}
      
      {/* Time Input Modal */}
      {selectedWordIndex !== null && (
        <TimeInputModal
          isOpen={isTimeModalOpen}
          currentTime={wordTimings[selectedWordIndex]?.startTime || 0}
          onSave={(time) => handleTimeUpdate(selectedWordIndex, time)}
          onClose={() => {
            setIsTimeModalOpen(false);
            setSelectedWordIndex(null);
          }}
          wordIndex={selectedWordIndex}
          word={wordTimings[selectedWordIndex]?.word || ''}
        />
      )}
    </div>
  );
};

export default AdvancedTranscriptEditor;
