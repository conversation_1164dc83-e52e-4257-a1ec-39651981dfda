/**
 * Utility functions for time calculations and formatting
 */

export interface WordTiming {
  word: string;
  startTime: number; // in seconds
  endTime: number; // in seconds
  isStickPoint?: boolean; // whether this word has a manually set time
}

/**
 * Format seconds to h:m:s format
 */
export const formatTimeHMS = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Parse h:m:s format to seconds
 */
export const parseTimeHMS = (timeStr: string): number => {
  const parts = timeStr.split(':').map(part => parseInt(part, 10));
  
  if (parts.length === 2) {
    // m:s format
    return parts[0] * 60 + parts[1];
  } else if (parts.length === 3) {
    // h:m:s format
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  }
  
  return 0;
};

/**
 * Calculate word timings based on total duration and word count
 */
export const calculateWordTimings = (
  text: string,
  totalDuration: number,
  existingStickPoints: { wordIndex: number; time: number }[] = []
): WordTiming[] => {
  const words = text.trim().split(/\s+/).filter(word => word.length > 0);
  const wordCount = words.length;

  if (wordCount === 0) return [];

  // Sort stick points by word index and remove duplicates
  const sortedStickPoints = [...existingStickPoints]
    .sort((a, b) => a.wordIndex - b.wordIndex)
    .filter((point, index, arr) => index === 0 || point.wordIndex !== arr[index - 1].wordIndex);

  // Initialize all word timings first
  const wordTimings: WordTiming[] = words.map((word, index) => ({
    word,
    startTime: 0,
    endTime: 0,
    isStickPoint: sortedStickPoints.some(sp => sp.wordIndex === index)
  }));

  // Calculate timings for segments between stick points
  for (let i = 0; i <= sortedStickPoints.length; i++) {
    const startIndex = i === 0 ? 0 : sortedStickPoints[i - 1].wordIndex;
    const endIndex = i === sortedStickPoints.length ? wordCount - 1 : sortedStickPoints[i].wordIndex;
    const startTime = i === 0 ? 0 : sortedStickPoints[i - 1].time;
    const endTime = i === sortedStickPoints.length ? totalDuration : sortedStickPoints[i].time;

    const segmentWordCount = endIndex - startIndex + 1;
    const segmentDuration = endTime - startTime;
    const timePerWord = segmentDuration / segmentWordCount;

    // Update timings for words in this segment
    for (let j = 0; j < segmentWordCount; j++) {
      const wordIndex = startIndex + j;
      const wordStartTime = startTime + (j * timePerWord);
      const wordEndTime = startTime + ((j + 1) * timePerWord);

      wordTimings[wordIndex].startTime = wordStartTime;
      wordTimings[wordIndex].endTime = wordEndTime;
    }
  }

  return wordTimings;
};

/**
 * Update word timings when a stick point is moved
 */
export const updateWordTimingsWithStickPoint = (
  currentTimings: WordTiming[],
  wordIndex: number,
  newTime: number,
  totalDuration: number
): WordTiming[] => {
  const text = currentTimings.map(wt => wt.word).join(' ');
  
  // Get existing stick points
  const existingStickPoints = currentTimings
    .map((wt, index) => ({ wordIndex: index, time: wt.startTime, isStick: wt.isStickPoint }))
    .filter(sp => sp.isStick)
    .map(sp => ({ wordIndex: sp.wordIndex, time: sp.time }));
  
  // Update or add the new stick point
  const stickPointIndex = existingStickPoints.findIndex(sp => sp.wordIndex === wordIndex);
  if (stickPointIndex >= 0) {
    existingStickPoints[stickPointIndex].time = newTime;
  } else {
    existingStickPoints.push({ wordIndex, time: newTime });
  }
  
  // Recalculate all timings
  return calculateWordTimings(text, totalDuration, existingStickPoints);
};

/**
 * Get the closest valid time for a word based on constraints
 */
export const getValidTimeForWord = (
  wordIndex: number,
  proposedTime: number,
  currentTimings: WordTiming[],
  totalDuration: number
): number => {
  // Find previous and next stick points
  let prevStickTime = 0;
  let nextStickTime = totalDuration;
  
  for (let i = 0; i < currentTimings.length; i++) {
    if (currentTimings[i].isStickPoint) {
      if (i < wordIndex) {
        prevStickTime = Math.max(prevStickTime, currentTimings[i].startTime);
      } else if (i > wordIndex) {
        nextStickTime = Math.min(nextStickTime, currentTimings[i].startTime);
        break;
      }
    }
  }
  
  // Ensure the proposed time is within bounds
  return Math.max(prevStickTime, Math.min(nextStickTime, proposedTime));
};
