// Simulate the frontend's calculateWordTimings function
function calculateWordTimings(text, totalDuration, existingStickPoints = []) {
  const words = text.trim().split(/\s+/).filter(word => word.length > 0);
  const wordCount = words.length;

  if (wordCount === 0) return [];

  // Sort stick points by word index and remove duplicates
  const sortedStickPoints = [...existingStickPoints]
    .sort((a, b) => a.wordIndex - b.wordIndex)
    .filter((point, index, arr) => index === 0 || point.wordIndex !== arr[index - 1].wordIndex);

  // Initialize all word timings first
  const wordTimings = words.map((word, index) => ({
    word,
    startTime: 0,
    endTime: 0,
    isStickPoint: sortedStickPoints.some(sp => sp.wordIndex === index)
  }));

  // Calculate timings for segments between stick points
  for (let i = 0; i <= sortedStickPoints.length; i++) {
    const startIndex = i === 0 ? 0 : sortedStickPoints[i - 1].wordIndex;
    const endIndex = i === sortedStickPoints.length ? wordCount - 1 : sortedStickPoints[i].wordIndex;
    const startTime = i === 0 ? 0 : sortedStickPoints[i - 1].time;
    const endTime = i === sortedStickPoints.length ? totalDuration : sortedStickPoints[i].time;

    const segmentWordCount = endIndex - startIndex + 1;
    const segmentDuration = endTime - startTime;
    const timePerWord = segmentDuration / segmentWordCount;

    // Update timings for words in this segment
    for (let j = 0; j < segmentWordCount; j++) {
      const wordIndex = startIndex + j;
      const wordStartTime = startTime + (j * timePerWord);
      const wordEndTime = startTime + ((j + 1) * timePerWord);

      wordTimings[wordIndex].startTime = wordStartTime;
      wordTimings[wordIndex].endTime = wordEndTime;
    }
  }

  return wordTimings;
}

// Test with the same data as the frontend would use
const text = "Hello world this is a test transcript";
const duration = 60;

const advancedWordTimings = calculateWordTimings(text, duration);

console.log('Advanced word timings:');
advancedWordTimings.forEach((wt, index) => {
  console.log(`${index}: "${wt.word}" ${wt.startTime} - ${wt.endTime}`);
});

// Create the data structure that would be sent to the API
const transcriptText = advancedWordTimings.map(wt => wt.word).join(' ');
const transcriptWithTimestamps = [{
  text: transcriptText,
  startTime: 0,
  endTime: duration,
  words: advancedWordTimings.map(word => ({
    word: word.word,
    startTime: word.startTime,
    endTime: word.endTime
  }))
}];

const videoData = {
  title: "Test Video with Word Timing",
  description: "A test video to debug the 400 error",
  duration: duration,
  transcript: transcriptText,
  transcriptSegments: transcriptWithTimestamps
};

console.log('\nVideo data that would be sent:');
console.log(JSON.stringify(videoData, null, 2));
