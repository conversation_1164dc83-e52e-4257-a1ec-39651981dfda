const axios = require('axios');

// Test data that simulates what the frontend would send
const testVideoData = {
  title: "Test Video with Word Timing",
  description: "A test video to debug the 400 error",
  duration: 60, // 60 seconds
  transcript: "Hello world this is a test transcript",
  transcriptSegments: [{
    text: "Hello world this is a test transcript",
    startTime: 0,
    endTime: 60,
    words: [
      { word: "Hello", startTime: 0, endTime: 8.571428571428571 },
      { word: "world", startTime: 8.571428571428571, endTime: 17.142857142857142 },
      { word: "this", startTime: 17.142857142857142, endTime: 25.714285714285715 },
      { word: "is", startTime: 25.714285714285715, endTime: 34.285714285714285 },
      { word: "a", startTime: 34.285714285714285, endTime: 42.857142857142854 },
      { word: "test", startTime: 42.857142857142854, endTime: 51.42857142857143 },
      { word: "transcript", startTime: 51.42857142857143, endTime: 60 }
    ]
  }]
};

async function testVideoCreation() {
  try {
    console.log('Sending test video data:', JSON.stringify(testVideoData, null, 2));
    
    const response = await axios.post('http://localhost:5104/api/videos', testVideoData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Success! Response:', response.data);
  } catch (error) {
    console.error('Error:', error.response?.status, error.response?.statusText);
    console.error('Error data:', error.response?.data);
    console.error('Full error:', error.message);
  }
}

testVideoCreation();
