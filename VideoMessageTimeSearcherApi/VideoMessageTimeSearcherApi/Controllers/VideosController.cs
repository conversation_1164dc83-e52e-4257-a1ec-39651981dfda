using Microsoft.AspNetCore.Mvc;
using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;
using VideoMessageTimeSearcherApi.Services.Interfaces;

namespace VideoMessageTimeSearcherApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class VideosController : ControllerBase
    {
        private readonly IVideoService _videoService;
        private readonly ILogger<VideosController> _logger;

        public VideosController(IVideoService videoService, ILogger<VideosController> logger)
        {
            _videoService = videoService;
            _logger = logger;
        }

        [HttpGet("search")]
        public async Task<ActionResult<PagedResult<VideoDto>>> SearchVideos(
            [FromQuery] string searchText = "",
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string sortBy = "createdAt",
            [FromQuery] bool sortDescending = true)
        {
            try
            {
                var request = new SearchVideosRequest
                {
                    SearchText = searchText,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    SortBy = sortBy,
                    SortDescending = sortDescending
                };

                var result = await _videoService.SearchVideosAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching videos");
                return StatusCode(500, "An error occurred while processing your request.");
            }
        }

        [HttpGet("search-transcripts")]
        public async Task<ActionResult<List<WordOccurrenceDto>>> SearchTranscripts(
            [FromQuery] string searchText,
            [FromQuery] string? languageCode = null,
            [FromQuery] bool matchWholeWord = false,
            [FromQuery] bool matchCase = false)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchText))
                {
                    return BadRequest("Search text is required.");
                }

                var request = new SearchTranscriptRequest
                {
                    SearchText = searchText,
                    LanguageCode = languageCode,
                    MatchWholeWord = matchWholeWord,
                    MatchCase = matchCase
                };

                var result = await _videoService.SearchTranscriptsAsync(request);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching transcripts");
                return StatusCode(500, "An error occurred while processing your request.");
            }
        }

        #region Admin Video Endpoints

        [HttpPost]
        public async Task<ActionResult<VideoDto>> CreateVideo([FromBody] CreateVideoRequest request)
        {
            try
            {
                _logger.LogInformation("Received CreateVideo request: {@Request}", request);

                if (request == null)
                {
                    _logger.LogWarning("CreateVideo request is null");
                    return BadRequest("Request cannot be null");
                }

                var result = await _videoService.CreateVideoAsync(request);
                return CreatedAtAction(nameof(GetVideo), new { id = result.Id }, result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Validation error creating video");
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating video");
                return StatusCode(500, "An error occurred while creating the video.");
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<VideoDto>> GetVideo(int id)
        {
            try
            {
                var video = await _videoService.GetVideoByIdAsync(id);
                if (video == null) return NotFound();
                return Ok(video);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting video with ID {id}");
                return StatusCode(500, "An error occurred while retrieving the video.");
            }
        }

        [HttpGet("{id}/transcript-segments")]
        public async Task<ActionResult<List<WordOccurrenceDto>>> GetVideoTranscriptSegments(int id)
        {
            try
            {
                var segments = await _videoService.GetVideoTranscriptSegmentsAsync(id);
                return Ok(segments);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting transcript segments for video with ID {id}");
                return StatusCode(500, "An error occurred while retrieving the transcript segments.");
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateVideo(int id, [FromBody] CreateVideoRequest request)
        {
            try
            {
                var success = await _videoService.UpdateVideoAsync(id, request);
                if (!success) return NotFound();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating video with ID {id}");
                return StatusCode(500, "An error occurred while updating the video.");
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteVideo(int id)
        {
            try
            {
                var success = await _videoService.DeleteVideoAsync(id);
                if (!success) return NotFound();
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting video with ID {id}");
                return StatusCode(500, "An error occurred while deleting the video.");
            }
        }

        #endregion


    }
}
