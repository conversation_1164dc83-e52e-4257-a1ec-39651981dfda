using VideoMessageTimeSearcherApi.DTOs;
using VideoMessageTimeSearcherApi.Models;

namespace VideoMessageTimeSearcherApi.Repositories.Interfaces
{
    public interface IVideoRepository
    {
        // Search operations
        Task<PagedResult<Video>> SearchVideosAsync(SearchVideosRequest request);
        Task<List<WordOccurrenceDto>> SearchTranscriptsAsync(SearchTranscriptRequest request);
        
        // Video operations
        Task<Video> GetVideoByIdAsync(int id);
        Task<Video> CreateVideoAsync(Video video);
        Task<bool> UpdateVideoAsync(Video video);
        Task<bool> DeleteVideoAsync(int id);
        Task<List<TranscriptSegmentGroup>> GetVideoTranscriptSegmentsAsync(int videoId);
        
        // Segment group operations
        Task<TranscriptSegmentGroup> GetSegmentGroupByIdAsync(int id);
        Task<TranscriptSegmentGroup> CreateSegmentGroupAsync(TranscriptSegmentGroup segmentGroup);
        Task<bool> DeleteSegmentGroupAsync(int id);
        
        // Segment operations
        Task<TranscriptSegment> GetSegmentByIdAsync(int id);
        Task<TranscriptSegment> CreateSegmentAsync(TranscriptSegment segment);
        Task<bool> DeleteSegmentAsync(int id);
        
        // Word timing operations
        Task<WordTiming> GetWordTimingByIdAsync(int id);
        Task<WordTiming> CreateWordTimingAsync(WordTiming wordTiming);
        Task<bool> DeleteWordTimingAsync(int id);
    }
}
